#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class TypeScriptErrorFixer {
  constructor() {
    this.errors = [];
    this.fixedCount = 0;
  }

  // Get all TypeScript errors
  getTypeScriptErrors() {
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      return [];
    } catch (error) {
      const output = error.stdout.toString();
      return this.parseErrors(output);
    }
  }

  // Parse TypeScript compiler output into structured errors
  parseErrors(output) {
    const errors = [];
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes(' - error TS')) {
        const match = line.match(/^(.+):(\d+):(\d+) - error TS(\d+): (.+)$/);
        if (match) {
          const [, file, lineNum, column, code, message] = match;
          errors.push({
            file: file.trim(),
            line: parseInt(lineNum),
            column: parseInt(column),
            code: parseInt(code),
            message: message.trim(),
            category: this.categorizeError(parseInt(code), message)
          });
        }
      }
    }
    
    return errors;
  }

  // Categorize errors by type for prioritization
  categorizeError(code, message) {
    // Unused variable errors (safest to fix)
    if (code === 6133) return 'unused_variable';
    
    // Missing import errors
    if (code === 2307) return 'missing_import';
    
    // Type annotation errors
    if (code === 7006) return 'type_annotation';
    
    // Property access errors
    if (code === 2339 || code === 18048) return 'property_access';
    
    // Function signature errors
    if (code === 2554 || code === 2345) return 'function_signature';
    
    // Interface/type errors
    if (code === 2322 || code === 2741 || code === 2740) return 'interface_mismatch';
    
    return 'other';
  }

  // Get the next simplest error to fix
  getNextError() {
    const errors = this.getTypeScriptErrors();
    
    if (errors.length === 0) {
      console.log('🎉 No TypeScript errors found!');
      return null;
    }

    // Prioritize by category (simplest first)
    const priority = ['unused_variable', 'missing_import', 'type_annotation', 'property_access', 'function_signature', 'interface_mismatch', 'other'];
    
    for (const category of priority) {
      const categoryErrors = errors.filter(e => e.category === category);
      if (categoryErrors.length > 0) {
        return categoryErrors[0];
      }
    }
    
    return errors[0];
  }

  // Count total errors
  countErrors() {
    return this.getTypeScriptErrors().length;
  }

  // Display error summary
  showErrorSummary() {
    const errors = this.getTypeScriptErrors();
    const categories = {};
    
    errors.forEach(error => {
      categories[error.category] = (categories[error.category] || 0) + 1;
    });

    console.log(`\n📊 TypeScript Error Summary (${errors.length} total):`);
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`  ${category}: ${count}`);
    });
    console.log('');
  }

  // Show next error to fix
  showNextError() {
    const error = this.getNextError();
    if (!error) return null;

    console.log(`\n🎯 Next Error to Fix:`);
    console.log(`File: ${error.file}`);
    console.log(`Line: ${error.line}, Column: ${error.column}`);
    console.log(`Code: TS${error.code}`);
    console.log(`Category: ${error.category}`);
    console.log(`Message: ${error.message}`);
    console.log('');

    return error;
  }
}

// CLI interface
if (require.main === module) {
  const fixer = new TypeScriptErrorFixer();
  const command = process.argv[2];

  switch (command) {
    case 'count':
      console.log(`Total TypeScript errors: ${fixer.countErrors()}`);
      break;
    case 'summary':
      fixer.showErrorSummary();
      break;
    case 'next':
      fixer.showNextError();
      break;
    default:
      console.log('Usage: node typescript-error-fixer.js [count|summary|next]');
      console.log('  count   - Show total error count');
      console.log('  summary - Show error breakdown by category');
      console.log('  next    - Show next error to fix');
  }
}

module.exports = TypeScriptErrorFixer;